// https://nuxt.com/docs/api/configuration/nuxt-config

export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',
  devtools: { enabled: true },
  pages: true,
  ssr: false,
  routeRules: {
    '/': {
      redirect: '/dashboard'
    }
  },
  modules: [
    '@nuxt/test-utils/module',
    '@nuxtjs/i18n',
    '@nuxtjs/tailwindcss',
    '@nuxtjs/color-mode',
    '@nuxt/icon',
    '@nuxt/image',
    '@pinia/nuxt',
    '@primevue/nuxt-module',
    '@nuxt/eslint',
    '@vee-validate/nuxt'
  ],
  plugins: [
    { src: '~/plugins/msw.client.ts', mode: 'client' },
    { src: '~/plugins/user.ts' }
  ],
  experimental: {
    typedPages: false,
    payloadExtraction: false,
    viewTransition: false,
    asyncContext: false,
    treeshakeClientOnly: false
  },
  app: {
    head: {
      title: 'KI-note',
      meta: [
        {
          name: 'viewport',
          content: 'width=device-width, initial-scale=1'
        },
        { name: 'description', content: 'AI-Powered Note App' }
      ],
      link: [
        { rel: 'icon', type: 'image/png', href: '/favicon.ico' },
        {
          rel: 'stylesheet',
          href: 'https://api.fontshare.com/v2/css?f[]=satoshi@1&display=swap'
        }
      ]
    }
  },
  css: [
    '@/assets/styles/tailwind.css',
    '@/assets/styles/theme.scss',
    '@/assets/styles/colors.scss',
    '@/assets/styles/toast.scss'
  ],
  postcss: {
    plugins: {
      '@tailwindcss/postcss': {},
      autoprefixer: {}
    }
  },
  i18n: {
    defaultLocale: 'de',
    locales: [
      { code: 'en', name: 'English', file: 'en.json' },
      { code: 'de', name: 'German', file: 'de.json' }
    ],
    lazy: true,
    langDir: 'locales/',
    detectBrowserLanguage: {
      useCookie: true,
      alwaysRedirect: true
    },
    bundle: {
      optimizeTranslationDirective: false
    }
  },
  icon: {
    mode: 'css',
    class: 'icon',
    cssLayer: 'base',
    size: '24px',
    serverBundle: {
      collections: ['uil', 'mdi']
    }
  },
  primevue: {
    autoImport: true,
    components: {
      prefix: 'Prime'
    }
  }
});
