import { useI18n } from 'vue-i18n';

export function useFormOptions() {
  const { t } = useI18n();

  const languageOptions = [
    { label: t('dashboard.settings.language.de'), value: 'de' },
    { label: t('dashboard.settings.language.en'), value: 'en' },
    { label: t('dashboard.settings.language.fr'), value: 'fr' }
  ];

  const structureOptions = [
    { label: t('dashboard.settings.structure.standard'), value: 'standard' },
    { label: t('dashboard.settings.structure.agenda'), value: 'agenda' },
    { label: t('dashboard.settings.structure.todo'), value: 'todo' }
  ];

  const designOptions = [
    { label: t('dashboard.settings.design.standard'), value: 'standard' },
    { label: t('dashboard.settings.design.minimal'), value: 'minimal' },
    { label: t('dashboard.settings.design.business'), value: 'business' }
  ];

  const folderOptions = [
    { label: t('dashboard.settings.folder.none'), value: 'none' }
  ];

  const transcribeOptions = [
    {
      icon: 'mdi:check',
      label: t('dashboard.settings.transcript.yes'),
      value: true
    },
    {
      icon: 'mdi:close',
      label: t('dashboard.settings.transcript.no'),
      value: false
    }
  ];

  const transcriptTypeOptions = [
    { label: t('dashboard.settings.transcript.verbatim'), value: 'verbatim' },
    { label: t('dashboard.settings.transcript.summary'), value: 'summary' },
    { label: t('dashboard.settings.transcript.semantic'), value: 'semantic' }
  ];

  return {
    languageOptions,
    structureOptions,
    designOptions,
    folderOptions,
    transcribeOptions,
    transcriptTypeOptions
  };
}
