<template>
  <div class="container pb-10!">
    <h1 class="card-title">{{ $t('support.title') }}</h1>
    <div class="card max-w-3xl! md:max-w-4xl!">
      <PrimeForm v-if="!isSubmitting" class="form" @submit="submitForm">
        <div class="form-row">
          <div class="form-group">
            <label for="name">{{ $t('support.form.name') }}</label>
            <PrimeInputText
              v-model="form.name"
              input-id="name"
              type="text"
              required
              :placeholder="$t('support.form.name_placeholder')"
            />
          </div>
          <div class="form-group">
            <label for="email">{{ $t('support.form.email') }}</label>
            <PrimeInputText
              v-model="form.email"
              input-id="email"
              type="email"
              required
              :placeholder="$t('support.form.email_placeholder')"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="category">{{ $t('support.form.category.title') }}</label>
          <PrimeSelect
            v-model="form.category"
            :options="categoryOptions"
            input-id="category"
            option-label="label"
            option-value="value"
            class="form-select"
            required
            :placeholder="$t('support.form.category.placeholder')"
            :pt="{
              overlay: { class: 'dropdown-overlay' },
              option: { class: 'options-label' }
            }"
          />
        </div>

        <div class="form-group">
          <label for="message">{{ $t('support.form.message') }}</label>
          <PrimeTextarea
            v-model="form.message"
            input-id="message"
            class="form-textarea no-resize"
            rows="4"
            required
            :placeholder="$t('support.form.message_placeholder')"
          />
        </div>

        <PrimeButton
          type="submit"
          class="submit-button"
          :loading="isSubmitting"
          rounded
        >
          {{ $t('support.form.send') }}
        </PrimeButton>
      </PrimeForm>
      <div v-else class="flex">
        <LoadingSpinner />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useToast } from 'primevue/usetoast';
  import { ref, reactive } from 'vue';
  import { useI18n } from 'vue-i18n';

  definePageMeta({
    middleware: 'auth'
  });

  const { t } = useI18n();
  const toast = useToast();

  const form = reactive({
    name: '',
    email: '',
    category: '',
    message: ''
  });

  const isSubmitting = ref(false);

  const categoryOptions = [
    { label: t('support.form.category.technical'), value: 'technical' },
    { label: t('support.form.category.billing'), value: 'billing' },
    { label: t('support.form.category.feature'), value: 'feature' },
    { label: t('support.form.category.other'), value: 'other' }
  ];

  const submitForm = async () => {
    isSubmitting.value = true;
    try {
      const result = await $fetch<string>('/api/support', {
        method: 'POST',
        body: {
          name: form.name,
          email: form.email,
          category: form.category,
          message: form.message
        }
      });

      Object.assign(form, {
        name: '',
        email: '',
        category: '',
        message: ''
      });

      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: result,
        life: 3000
      });
    } catch {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Error submitting form. Please try again.',
        life: 3000
      });
    } finally {
      isSubmitting.value = false;
    }
  };
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/utils' as *;
  @use '@/assets/styles/colors' as *;
</style>
