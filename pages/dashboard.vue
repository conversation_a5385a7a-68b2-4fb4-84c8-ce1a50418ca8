<template>
  <UIContainer>
    <template #header>
      <!-- Header: Title -->
      <h1 class="card-title text-center! w-full!">
        {{ $t('dashboard.title') }}
      </h1>
    </template>

    <!-- Main Content -->
    <!-- 1. Step: File Upload Section -->
    <DashboardFileUpload v-if="!uploadedFile && !isProcessing" @upload="uploadedFile = $event" />

    <!-- 2. Step: File Preview -->
    <DashboardFilePreview v-if="uploadedFile && !isProcessing" :uploaded-file="uploadedFile"
      @remove="uploadedFile = null" @transcribe="showTranscriptionModal = true" />

    <!-- 3. Step: File Individual Settings Modal-->
    <DashboardFileSettings v-model="transcriptionSettings" :show-transcription-modal="showTranscriptionModal"
      @update:show-transcription-modal="showTranscriptionModal = $event" @submit="startTranscription" />

    <!-- 4. Step: Transcription Processing State -->
    <FileProcessing v-if="isProcessing" :transcription-store="transcriptionStore" />

    <template #footer>
      <!-- Footer: Feature Cards and Hint -->
      <DashboardFeatureCards />
      <div class="text-center text-sm text-white/50">
        KI-note kann Fehler machen. Überprüfe wichtige Informationen.
      </div>
    </template>
  </UIContainer>
</template>

<script lang="ts" setup>
import { ref, onUnmounted } from 'vue';
import { useTranscriptionStore } from '~/stores/transcription';
import { useToast } from 'primevue/usetoast';
import { useI18n } from 'vue-i18n';
import FileProcessing from '~/components/Dashboard/FileProcessing.vue';
import { TranscriptionSettings } from '~/types/Transcription';
import type {
  TranscriptionProgress,
  UploadedFile
} from '~/types/Transcription';

definePageMeta({
  middleware: 'auth'
});

const transcriptionStore = useTranscriptionStore();
const notesStore = useNotesStore();
const userStore = useUserStore();
const toast = useToast();
const { t } = useI18n();

// File upload state
const uploadedFile = ref<UploadedFile | null>(null);
const showTranscriptionModal = ref(false);
const isProcessing = ref(false);
let progressInterval: ReturnType<typeof setInterval> | undefined = undefined;

// Transcription settings
const transcriptionSettings = ref(new TranscriptionSettings());

// Store the current note ID being processed
const currentNoteId = ref<number | null>(null);

// Poll for transcription progress
const pollProgress = async () => {
  if (!transcriptionStore.isProcessing) return;

  try {
    const response = await $fetch<TranscriptionProgress>(
      '/api/transcription/progress'
    );
    transcriptionStore.updateProgress(
      response.progress,
      response.estimatedTime
    );

    // Update note status in the notes store
    if (currentNoteId.value) {
      if (response.status === 'completed') {
        notesStore.updateNoteStatus(currentNoteId.value, 'completed');
        transcriptionStore.completeTranscription();
        clearInterval(progressInterval);
        isProcessing.value = false;
        uploadedFile.value = null;
        currentNoteId.value = null;

        // Show completion message
        toast.add({
          severity: 'success',
          summary: t('dashboard.transcription_completed'),
          detail: t('dashboard.note_ready'),
          life: 5000
        });
      } else {
        // Keep status as processing
        notesStore.updateNoteStatus(currentNoteId.value, 'processing');
      }
    }
  } catch (error) {
    console.error('Failed to fetch progress:', error);

    // Update note status to failed on error
    if (currentNoteId.value) {
      notesStore.updateNoteStatus(currentNoteId.value, 'failed');
      toast.add({
        severity: 'error',
        summary: t('dashboard.transcription_failed'),
        detail: t('dashboard.transcription_error_detail'),
        life: 5000
      });
    }
  }
};

onUnmounted(() => {
  if (progressInterval) {
    clearInterval(progressInterval);
  }
});

const startTranscription = async () => {
  try {
    if (!uploadedFile.value) return;

    const formData = new FormData();
    formData.append('file', uploadedFile.value.file);
    formData.append('settings', JSON.stringify(transcriptionSettings.value));

    // Start transcription in store
    transcriptionStore.startTranscription(uploadedFile.value.name);
    isProcessing.value = true;

    // Start polling for progress
    progressInterval = setInterval(pollProgress, 12000);
    pollProgress();

    // Check authentication before making API call
    if (!userStore.isAuthenticated || !userStore.token) {
      throw new Error('User not authenticated');
    }

    const response = await $fetch<UploadedFile & { noteId?: number }>('/api/note', {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${userStore.token}`
      }
    });

    showTranscriptionModal.value = false;
    transcriptionSettings.value = new TranscriptionSettings();

    // Update with server response
    uploadedFile.value = {
      ...uploadedFile.value,
      ...response.file,
      transcription: response.transcription
    };

    // Create note in the store if we have a noteId from the response
    if (response.noteId) {
      // Store the note ID for progress tracking
      currentNoteId.value = response.noteId;

      // The note was already created in the API handler, so we just need to load the notes
      await notesStore.loadUserNotes();

      // Show success message
      toast.add({
        severity: 'success',
        summary: t('dashboard.transcription_started'),
        detail: t('dashboard.note_created'),
        life: 3000
      });
    }

    // Complete transcription when done
    if (response.transcription?.status === 'completed') {
      transcriptionStore.completeTranscription();
      clearInterval(progressInterval);

      // Update note status to completed
      if (response.noteId) {
        notesStore.updateNoteStatus(response.noteId, 'completed');
      }
    }

    uploadedFile.value = null;
  } catch (error) {
    transcriptionStore.cancelTranscription();
    if (progressInterval) {
      clearInterval(progressInterval);
    }

    // Update note status to failed if we have a note ID
    if (currentNoteId.value) {
      notesStore.updateNoteStatus(currentNoteId.value, 'failed');
      currentNoteId.value = null;
    }

    toast.add({
      severity: 'error',
      summary: t('dashboard.transcription_error'),
      detail: error,
      life: 3000
    });
  }
};
</script>

<style lang="scss" scoped>
@use '@/assets/styles/theme' as *;
@use '@/assets/styles/utils' as *;
@use '@/assets/styles/colors' as *;
</style>
