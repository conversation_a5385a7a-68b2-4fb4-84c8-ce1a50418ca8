<template>
  <UIContainer>
    <template #header>
      <h1 class="card-title">
        {{ $t('titles.saved_notes') }}
      </h1>
    </template>

    <UIGlassWrapper class="overflow-hidden! px-1! md:px-8! h-[95%]!">
      <template #header>
        <div class="flex flex-row! gap-4 mr-auto w-full! xl:w-[75%]!">
          <UISearch v-model="searchQuery" class="flex-2" :placeholder="$t('saved_notes.search')" />
          <UISelect v-model="selectedFilter" class="flex-1" :placeholder="$t('saved_notes.filter')"
            :options="filterOptions" show-clear />
          <UISelect v-model="selectedAction" class="flex-1!" :placeholder="$t('saved_notes.select_options')"
            :options="actions" />
        </div>
      </template>

      <NotesTable :notes="filteredNotes" :loading="loading" class="h-[62vh]!" @download="handleDownload"
        @menu="handleMenu" @selection="handleSelectionChange" @delete="handleDelete" />
    </UIGlassWrapper>
  </UIContainer>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useToast } from 'primevue/usetoast';
import { Note } from '@/types/Note';

definePageMeta({
  middleware: 'auth'
});

const toast = useToast();
const loading = ref(false);
const notes = ref<Array<Note>>([
  new Note(
    1,
    'Gemeindesitzung 1aagkal',
    new Date('01.02.2025'),
    '820:33',
    'Gemeindesitzungen'
  ),
  new Note(
    2,
    'Jour Fixe Marketing',
    new Date('01.02.2025'),
    '20:33',
    'Jour Fixe'
  ),
  new Note(3, 'NameSchockolade', new Date('01.02.2025'), '20:33', null),
  new Note(4, 'Audio-KI-note2025.mp3', new Date('01.02.2025'), '20:33', null),
  new Note(
    5,
    'FoodRallyeTicketsKaufen',
    new Date('01.02.2025'),
    '20:33',
    'Marketing'
  ),
  new Note(
    6,
    'Gemeind5esitzung 1',
    new Date('01.02.2025'),
    '20:33',
    'Gemeindesitzungen'
  ),
  new Note(
    7,
    'Jour Fi5xe Marketing',
    new Date('01.02.2025'),
    '20:33',
    'Jour Fixe'
  ),
  new Note(8, 'NameSch5ockolade', new Date('01.02.2025'), '20:33', null),
  new Note(9, 'Audio-K5I-note2025.mp3', new Date('01.02.2025'), '20:33', null)
]);

const searchQuery = ref('');
const selectedFilter = ref(null);
const filterOptions = ref([
  { label: 'Folder 1', value: 'Gemeindesitzungen' },
  { label: 'Folder 2', value: 'Jour Fixe' }
]);
const selectedAction = ref(null);
const actions = ref([
  { label: 'Delete', value: 'delete' },
  { label: 'Download', value: 'download' }
]);
const selectedNotes = ref<Array<Note>>([]);

const filteredNotes = computed(() => {
  let result = [...notes.value];

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(
      (note) =>
        note.name.toLowerCase().includes(query) ||
        (note.folder && note.folder.toLowerCase().includes(query))
    );
  }

  // Apply filter
  if (selectedFilter.value) {
    result = result.filter((note) => note.folder === selectedFilter.value);
  }

  return result;
});

const handleDownload = async (_note: Note, format: string) => {
  try {
    const response = await fetch(`/api/download/${format}`, {
      method: 'GET'
    });

    if (!response.ok) throw new Error('Failed to download');

    const blob = await response.blob();

    // Get filename from headers (optional)
    const disposition = response.headers.get('Content-Disposition');
    const match = disposition?.match(/filename="?(.+?)"?$/);
    const filename = match?.[1] || 'downloaded-file';

    // Create a download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    window.URL.revokeObjectURL(url);
  } catch (err) {
    console.error('Download error:', err);
  }
};

const handleMenu = (note: Note) => {
  toast.add({
    severity: 'info',
    summary: 'Menu',
    detail: `Menu for: ${note.name}`,
    life: 3000
  });
};

const handleSelectionChange = (selectedIds: Array<number>) => {
  selectedNotes.value = filteredNotes.value.filter((note) =>
    selectedIds.includes(note.id)
  );
};
</script>
