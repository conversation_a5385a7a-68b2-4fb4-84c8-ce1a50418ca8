<template>
  <UIContainer>
    <template #header>
      <h1 class="card-title">
        {{ $t('titles.saved_notes') }}
      </h1>
    </template>

    <UIGlassWrapper class="overflow-hidden! px-1! md:px-8! h-[95%]!">
      <template #header>
        <div class="flex flex-row! gap-4 mr-auto w-full! xl:w-[75%]!">
          <UISearch v-model="searchQuery" class="flex-2" :placeholder="$t('saved_notes.search')" />
          <UISelect v-model="selectedFilter" class="flex-1" :placeholder="$t('saved_notes.filter')"
            :options="filterOptions" show-clear />
          <UISelect v-model="selectedAction" class="flex-1!" :placeholder="$t('saved_notes.select_options')"
            :options="actions" />
        </div>
      </template>

      <NotesTable :notes="filteredNotes" :loading="loading" class="h-[62vh]!" @download="handleDownload"
        @menu="handleMenu" @selection="handleSelectionChange" @delete="handleDelete" />
    </UIGlassWrapper>
  </UIContainer>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useToast } from 'primevue/usetoast';
import type { Note } from '@/types/Note';

definePageMeta({
  middleware: 'auth'
});

const toast = useToast();
const notesStore = useNotesStore();
const loading = computed(() => notesStore.isLoading);
const notes = computed(() => notesStore.userNotes);

// Load notes when component mounts
onMounted(async () => {
  await notesStore.loadUserNotes();
});

const searchQuery = ref('');
const selectedFilter = ref(null);
const selectedAction = ref(null);
const actions = ref([
  { label: 'Delete', value: 'delete' },
  { label: 'Download', value: 'download' }
]);
const selectedNotes = ref<Array<Note>>([]);

// Dynamic filter options based on available folders
const filterOptions = computed(() => {
  const folders = new Set<string>();
  notes.value.forEach(note => {
    if (note.folder) {
      folders.add(note.folder);
    }
  });

  return Array.from(folders).map(folder => ({
    label: folder,
    value: folder
  }));
});

const filteredNotes = computed(() => {
  let result = [...notes.value];

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(
      (note) =>
        note.name.toLowerCase().includes(query) ||
        (note.folder && note.folder.toLowerCase().includes(query))
    );
  }

  // Apply folder filter
  if (selectedFilter.value) {
    result = result.filter((note) => note.folder === selectedFilter.value);
  }

  return result;
});

const handleDownload = async (note: Note, format: string) => {
  try {
    const success = await notesStore.downloadNote(note.id, format as 'pdf' | 'docx');

    if (success) {
      toast.add({
        severity: 'success',
        summary: 'Download Started',
        detail: `Downloading ${note.name} as ${format.toUpperCase()}`,
        life: 3000
      });
    } else {
      throw new Error('Download failed');
    }
  } catch (err) {
    console.error('Download error:', err);
    toast.add({
      severity: 'error',
      summary: 'Download Failed',
      detail: 'Failed to download the note. Please try again.',
      life: 3000
    });
  }
};

const handleDelete = async (note: Note) => {
  try {
    const success = await notesStore.deleteNote(note.id);

    if (success) {
      toast.add({
        severity: 'success',
        summary: 'Note Deleted',
        detail: `${note.name} has been deleted successfully`,
        life: 3000
      });
    } else {
      throw new Error('Delete failed');
    }
  } catch (err) {
    console.error('Delete error:', err);
    toast.add({
      severity: 'error',
      summary: 'Delete Failed',
      detail: 'Failed to delete the note. Please try again.',
      life: 3000
    });
  }
};

const handleMenu = (note: Note) => {
  toast.add({
    severity: 'info',
    summary: 'Menu',
    detail: `Menu for: ${note.name}`,
    life: 3000
  });
};

const handleSelectionChange = (selectedIds: Array<number>) => {
  selectedNotes.value = filteredNotes.value.filter((note) =>
    selectedIds.includes(note.id)
  );
};
</script>
