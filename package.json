{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --host", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "test": "vitest", "lint": "eslint . --ext .vue,.js,.ts", "lint:fix": "eslint . --ext .vue,.js,.ts --fix", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky"}, "dependencies": {"@nuxt/eslint": "^1.5.2", "@pinia/nuxt": "^0.11.1", "@primeuix/themes": "^1.2.1", "@primevue/forms": "^4.3.6", "@vee-validate/nuxt": "^4.15.1", "@vee-validate/yup": "^4.15.1", "@vee-validate/zod": "^4.15.1", "pinia": "^3.0.3", "primevue": "^4.3.6", "vee-validate": "^4.15.1", "vue": "latest", "vue-router": "^4.5.1", "yup": "^1.6.1"}, "devDependencies": {"@nuxt/eslint-config": "^1.5.2", "@nuxt/icon": "^1.15.0", "@nuxt/image": "^1.10.0", "@nuxt/test-utils": "^3.19.2", "@nuxtjs/color-mode": "^3.5.2", "@nuxtjs/i18n": "^9.5.6", "@nuxtjs/tailwindcss": "^7.0.0-beta.0", "@primevue/nuxt-module": "^4.3.6", "@stylistic/eslint-plugin": "^5.1.0", "@tailwindcss/postcss": "^4.1.11", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitest/ui": "^3.2.4", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^10.3.0", "globals": "^16.3.0", "happy-dom": "^18.0.1", "husky": "^9.1.7", "jsdom": "^26.1.0", "msw": "^2.10.4", "nuxt": "^3.17.6", "prettier": "^3.6.2", "sass": "^1.89.2", "tailwindcss": "^4.1.11", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "msw": {"workerDirectory": ["public"]}, "lint-staged": {"*.{js,ts,vue}": ["prettier --write", "eslint --fix"]}}