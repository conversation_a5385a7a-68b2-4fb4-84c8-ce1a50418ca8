import { describe, it, expect } from 'vitest';
import { Note } from '~/types/Note';

describe('Note Class', () => {
  it('should create a note with correct properties', () => {
    const note = new Note(
      1,
      'Test Note',
      new Date('2025-01-22'),
      '10:30',
      'Test Folder',
      'completed',
      1,
      {
        fileId: 'file-123',
        transcriptionId: 'trans-123',
        fileUrl: '/test.mp3',
        fileSize: 1000000,
        fileType: 'audio/mpeg'
      }
    );

    expect(note.id).toBe(1);
    expect(note.name).toBe('Test Note');
    expect(note.status).toBe('completed');
    expect(note.userId).toBe(1);
    expect(note.fileId).toBe('file-123');
  });

  it('should update note status correctly', () => {
    const note = new Note(
      1,
      'Test Note',
      new Date(),
      '10:30',
      null,
      'processing',
      1
    );

    const originalUpdatedAt = note.updatedAt;

    // Wait a bit to ensure timestamp changes
    setTimeout(() => {
      note.updateStatus('completed');

      expect(note.status).toBe('completed');
      expect(note.updatedAt.getTime()).toBeGreater<PERSON>han(
        originalUpdatedAt.getTime()
      );
    }, 10);
  });

  it('should serialize and deserialize note correctly', () => {
    const originalNote = new Note(
      1,
      'Test Note',
      new Date('2025-01-22'),
      '10:30',
      'Test Folder',
      'completed',
      1,
      {
        fileId: 'file-123',
        transcriptionId: 'trans-123'
      }
    );

    const serialized = originalNote.toJSON();
    const deserialized = Note.fromJSON(serialized);

    expect(deserialized.id).toBe(originalNote.id);
    expect(deserialized.name).toBe(originalNote.name);
    expect(deserialized.status).toBe(originalNote.status);
    expect(deserialized.fileId).toBe(originalNote.fileId);
  });
});
