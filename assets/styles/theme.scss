@use '@/assets/styles/colors' as *;

/*========Global and Primevue Colors========*/
:root {
  --p-progressbar-background: #{$black} !important;
  --p-progressbar-value-background: #{$teal-light} !important;
  --p-progressbar-border-radius: 0.5rem !important;
  --p-progressbar-height: 0.5rem !important;
  --p-progressspinner-color-one: #{$purple-primary} !important;
  --p-progressspinner-color-two: #{$teal-dark} !important;
  --p-progressspinner-color-three: #{$purple-primary} !important;
  --p-progressspinner-color-four: #{$teal-dark} !important;
  --p-select-placeholder-color: #{$text-dark} !important;
  --p-password-strength-weak-background: #{$red-dark} !important;
  --p-password-strength-medium-background: #{$yellow-base} !important;
  --p-password-strength-strong-background: #{$green-dark} !important;

  --background-gradient: #{linear-gradient(
      315deg,
      $purple-primary -200%,
      $purple-deep,
      $teal-dark 250%
    )};
}

/*========Global Styles========*/
html,
body {
  font-family: 'Satoshi', sans-serif;
  font-size: clamp(16px, 0.75vw, 24px);
  color: $text-white;
  background: $black;
}
.bg-sidebar {
  background: $bg-sidebar;
}

main {
  margin-top: 0.5rem;
  flex: 1 1 0%;
  overflow-y: auto;
  background: var(--background-gradient);
  position: relative;
  overflow: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
  font-size: 1rem;

  @media (min-width: 768px) {
    border-top-left-radius: 1.5rem;
    padding: 1.5rem;
  }
}

h1 {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  color: $text-white;
}

h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: $text-white;
}

h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: $text-white;
}

div,
span,
p {
  color: $text-white;
}

/*========Link========*/
a {
  font-size: 1rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 12px;
  gap: 8px;
  span.dark {
    color: $text-dark;
  }
}

a:hover {
  background: $backdrop-3;
  border-radius: 6px;
}

.active-link {
  background: $backdrop-3;
  border-radius: 6px;
  span {
    color: $text-white;
  }
}

/*=========Horizontal Ruler========*/
.light-divider::before {
  border-top: 1px solid $backdrop-1 !important;
  border-block-start: 1px solid $backdrop-1 !important;
}

.card {
  background: $backdrop-6;
  border: 1px solid $backdrop-4;
  border-radius: 0.75rem;
  padding: 1.5rem;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

.card-title {
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: left;
}

/*=======FORM=======*/

.form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;

  @media (max-width: 1280px) {
    grid-template-columns: 1fr;
  }
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 1rem;

  & > * {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  @media (max-width: 768px) {
    grid-template-columns: repeat(1, 1fr);
  }
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

input,
.form-input,
.form-select,
.form-textarea {
  background: $backdrop-5 !important;
  border: 1px solid $backdrop-3 !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem !important;
  font-size: 1rem !important;
  color: $text-white !important;
  transition: all 0.2s ease !important;
  height: 3.25rem;

  &::placeholder {
    color: $backdrop-gray;
  }

  &:focus {
    outline: none;
    border-color: $text-white !important;
    background: $backdrop-5 !important;
  }

  &:hover {
    border-color: $backdrop-2 !important;
  }

  &.no-resize {
    resize: none !important;
  }
}

.form-password {
  @extend .form-input;

  border-radius: 0.5rem !important;
  &.meter:focus {
    border-bottom-left-radius: 0rem !important;
    border-bottom-right-radius: 0rem !important;
  }
}

.overlay {
  @extend .overflow-y-auto;
  background: $backdrop-black-1 !important;
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);

  box-shadow:
    -8px 0 4px -4px $backdrop-black-1,
    8px 0 4px -4px $backdrop-black-1,
    0 8px 4px 2px $backdrop-black-1 !important;

  border-bottom-left-radius: 0.5rem !important;
  border-bottom-right-radius: 0.5rem !important;
}

.password-overlay {
  @extend .overlay;

  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
}

.p-password-meter {
  height: 0.25rem !important;
  border-radius: 0.5rem !important;
}

.error-message {
  background: $red-transparent;
  border: 1px solid $red-soft;
  border-radius: 0.5rem;
  padding: 0.75rem;
  color: $red-light;
  font-size: 0.875rem;
  text-align: center;
}

.p-radiobutton {
  .p-radiobutton-box {
    background: $backdrop-5 !important;
    border: 1px solid $backdrop-3 !important;
    border-radius: 50% !important;
    width: 2rem !important;
    height: 2rem !important;
    margin: 0.5rem 0.25rem !important;
    transition: all 0.2s ease !important;

    &:hover {
      border-color: $hover-gray-solid !important;
    }

    &.p-highlight {
      background: $purple-primary !important;
      border-color: $purple-primary !important;
    }

    .p-radiobutton-icon {
      background: $white !important;
      width: 0.5rem !important;
      height: 0.5rem !important;
      border-radius: 50% !important;
    }
  }

  &:focus-within .p-radiobutton-box {
    outline: none !important;
    border-color: $text-white !important;
    box-shadow: 0 0 0 2px $backdrop-3 !important;
  }
}

.dropdown-overlay {
  @extend .overlay;
}

.options-label {
  width: 100%;
  padding: 0.5rem !important;
  padding-left: 0.75rem !important;
  font-size: 1rem;

  &:hover {
    background-color: $backdrop-4 !important;
  }

  &:last-child {
    border-bottom-left-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
  }
}

:deep(.p-select-open) {
  border-bottom-left-radius: 0rem !important;
  border-bottom-right-radius: 0rem !important;
}

legend {
  color: $text-dark;
  font-size: 0.875rem;
}

:deep(.p-invalid) {
  border-color: $red-salmon !important;
  color: $red-salmon !important;
}

.error-hint {
  color: $red-salmon;
  font-size: 0.875rem;
  margin-left: 0.75rem;
}

/*========Scrollbar========*/

.overflow-y-auto {
  overflow-y: auto !important;
  scrollbar-width: thin;
  scrollbar-color: $backdrop-2 transparent;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: $backdrop-2;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: $backdrop-gray;
}

.overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: $backdrop-2 transparent;
}

.overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: $white;
  border-radius: 10px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: $white;
  border-radius: 10px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: $backdrop-1;
}
