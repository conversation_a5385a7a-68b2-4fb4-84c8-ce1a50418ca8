<template>
  <div class="fixed bottom-4 left-4 bg-gray-800 text-white p-4 rounded-lg text-xs max-w-sm z-50">
    <h4 class="font-bold mb-2">Auth Debug Info</h4>
    <div class="space-y-1">
      <div>Authenticated: {{ userStore.isAuthenticated }}</div>
      <div>User: {{ userStore.user?.name || 'None' }}</div>
      <div>Token: {{ userStore.token ? 'Present' : 'None' }}</div>
      <div>Loading: {{ userStore.isLoading }}</div>
      <div>Notes Error: {{ notesStore.error || 'None' }}</div>
      <div>Notes Count: {{ notesStore.notes.length }}</div>
    </div>
    <div class="flex gap-2 mt-2">
      <button @click="testAuth" class="bg-blue-600 px-2 py-1 rounded text-xs">
        Test Auth
      </button>
      <button @click="refreshNotes" class="bg-green-600 px-2 py-1 rounded text-xs">
        Refresh Notes
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
const userStore = useUserStore();
const notesStore = useNotesStore();

const testAuth = async () => {
  console.log('Testing auth...');
  console.log('User store state:', {
    isAuthenticated: userStore.isAuthenticated,
    user: userStore.user,
    token: userStore.token ? 'Present' : 'None'
  });

  try {
    await notesStore.loadUserNotes();
    console.log('Notes loaded successfully');
  } catch (error) {
    console.error('Failed to load notes:', error);
  }
};
</script>
