<template>
  <UITable :items="notes" :paginator="true" :rows="6" :loading="loading" :total-records="notes.length"
    :scrollable="true">
    <template #empty>
      <div class="flex flex-col items-center max-w-xl text-center mx-auto!">
        <Icon name="mdi:note-outline" class="w-24 h-24" />
        <p class="text-2xl">{{ $t('saved_notes.no_notes') }}</p>
      </div>
    </template>

    <!-- Checkbox Column -->
    <PrimeColumn>
      <template #body="{ data }">
        <UICheckbox :model-value="selectionComputed(data).value" variant="primary" binary
          @update:model-value="selectionComputed(data).value = $event" />
      </template>
    </PrimeColumn>

    <!-- Protocol Name -->
    <PrimeColumn field="name" :header="$t('saved_notes.titles.name')" />

    <!-- Date -->
    <PrimeColumn field="date" :header="$t('saved_notes.titles.date')">
      <template #body="{ data }">
        <span>{{ data.date.toLocaleDateString(locale) }}</span>
      </template>
    </PrimeColumn>

    <!-- Duration -->
    <PrimeColumn field="duration" :header="$t('saved_notes.titles.duration')" />

    <!-- Folder -->
    <PrimeColumn field="folder" :header="$t('saved_notes.titles.folder')" />

    <!-- Status -->
    <PrimeColumn field="status" :header="$t('saved_notes.titles.status')">
      <template #body="{ data }">
        <div class="flex items-center gap-2">
          <Icon v-if="data.status === 'processing'" name="mdi:loading" class="animate-spin" />
          <Icon v-else-if="data.status === 'completed'" name="mdi:check-circle" />
          <Icon v-else-if="data.status === 'failed'" name="mdi:alert-circle" />
          <Icon v-else name="mdi:clock-outline" />
          <span class="text-sm">{{ $t('saved_notes.status.' + data.status) }}</span>
        </div>
      </template>
    </PrimeColumn>

    <!-- AI Assistant -->

    <!-- Actions -->
    <PrimeColumn>
      <template #header>
        <span class="w-[90%]! text-right!">{{
          $t('saved_notes.titles.actions')
        }}</span>
      </template>
      <template #body="{ data }">
        <div class="flex justify-end! items-center">
          <UIButton variant="icon" class="pr-1! rounded-e-none!" :disabled="data.status !== 'completed'"
            @click="$emit('download', data, 'docx')">
            <Icon name="mdi:tray-download" />
          </UIButton>
          <UIButton variant="icon" class="pl-0! rounded-s-none!" :disabled="data.status !== 'completed'"
            @click="toggle">
            <Icon name="mdi:chevron-down" />
          </UIButton>
          <UIPopOverList ref="downloadMenu" append-to="body">
            <ul>
              <li class="popover-label" @click="data.status === 'completed' && $emit('download', data, 'docx')">
                {{ $t('saved_notes.download.docx') }}
              </li>
              <li class="popover-label" @click="data.status === 'completed' && $emit('download', data, 'pdf')">
                {{ $t('saved_notes.download.pdf') }}
              </li>
            </ul>
          </UIPopOverList>
          <UIButton variant="icon" @click="$emit('delete', data)">
            <Icon name="mdi:trashcan-outline" />
          </UIButton>
        </div>
      </template>
    </PrimeColumn>
  </UITable>
</template>

<script lang="ts" setup>
import type { Note } from '@/types/Note';
import { ref, onMounted, onUnmounted } from 'vue';

defineProps({
  notes: {
    type: Array<Note>,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['download', 'menu', 'selection', 'delete']);

onMounted(() => {
  window.addEventListener('resize', updateIsDesktop);
});

onUnmounted(() => {
  window.removeEventListener('resize', updateIsDesktop);
});

const selectedItems = defineModel<Note[]>('selection', { default: () => [] });
const isDesktop = ref(window.innerWidth >= 768);
const locale = useI18n().locale.value;
const downloadMenu = ref();

const toggle = (event: PointerEvent) => {
  downloadMenu.value.toggle(event);
};

function updateIsDesktop() {
  isDesktop.value = window.innerWidth >= 768 && window.innerHeight >= 640;
}

function selectionComputed(note: Note) {
  return computed({
    get: () => selectedItems.value.some((i) => i.id === note.id),
    set: (checked: boolean) => {
      const index = selectedItems.value.findIndex((i) => i.id === note.id);
      if (checked && index === -1) {
        selectedItems.value.push(note);
      } else if (!checked && index !== -1) {
        selectedItems.value.splice(index, 1);
      }

      emit(
        'selection',
        selectedItems.value.map((i) => i.id)
      );
    }
  });
}
</script>

<style lang="scss" scoped>
@use '@/assets/styles/theme' as *;
@use '@/assets/styles/colors' as *;

:deep(.p-datatable-paginator-bottom) {
  border: 0;
}
</style>
