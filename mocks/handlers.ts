import { http, HttpResponse, delay } from 'msw';
import { User } from '~/types/User';
import type { UserProfile } from '~/types/User';
import type { TranscriptionProgress } from '~/types/Transcription';

// Mock database of users
const mockUsers = [
  new User(
    1,
    'Alice Wonderland',
    'AW',
    500,
    200,
    'Premium',
    {
      firstName: 'Alice',
      lastName: 'Wonderland',
      businessName: 'Alice Wonderland',
      phoneNumber: '1234567890',
      email: '<EMAIL>'
    } as UserProfile,
    '<EMAIL>'
  ),
  new User(
    2,
    '<PERSON> Dylan',
    'BD',
    300,
    25,
    'Free',
    {
      firstName: 'Bob',
      lastName: '<PERSON>',
      businessName: '<PERSON> Dylan',
      phoneNumber: '0987654321',
      email: '<EMAIL>'
    } as UserProfile,
    '<EMAIL>'
  )
];

// Mock authentication tokens
const mockTokens = new Map<string, number>();

// Generate a simple mock token
function generateToken(userId: number): string {
  const token = `mock-token-${userId}-${Date.now()}`;
  mockTokens.set(token, userId);
  return token;
}

// Validate token and return user ID
function validateToken(token: string): number | null {
  return mockTokens.get(token) || null;
}

export const handlers = [
  http.get('/api/user/:id', ({ params }) => {
    const userId = Number(params.id);
    const user = mockUsers.find((u) => u.id === userId);

    if (user) {
      return HttpResponse.json({ user });
    } else {
      return HttpResponse.json({ error: 'User not found' }, { status: 404 });
    }
  }),

  http.post('/api/login', async ({ request }) => {
    const body = (await request.json().catch(() => ({}))) as {
      username?: string;
      password?: string;
    };

    // Mock authentication logic
    let user: User | undefined;
    if (body.username === 'admin' && body.password === 'admin') {
      user = mockUsers[0]; // Alice
    } else if (body.username === 'user' && body.password === 'user') {
      user = mockUsers[1]; // Bob
    }

    if (user) {
      const token = generateToken(user.id);
      return HttpResponse.json({
        success: true,
        user,
        token,
        message: 'Login successful'
      });
    }

    return HttpResponse.json(
      {
        success: false,
        message: 'Invalid username or password'
      },
      { status: 401 }
    );
  }),

  http.post('/api/logout', async ({ request }) => {
    const body = (await request.json().catch(() => ({}))) as {
      token?: string;
      id?: number;
    };

    // Remove token from mock storage
    if (body.token) {
      mockTokens.delete(body.token);
    }

    return HttpResponse.json({
      success: true,
      message: 'Logout successful'
    });
  }),

  http.post('/api/verify-token', async ({ request }) => {
    const body = (await request.json().catch(() => ({}))) as {
      token?: string;
    };

    if (!body.token) {
      return HttpResponse.json({ error: 'Token required' }, { status: 400 });
    }

    const userId = validateToken(body.token);
    if (userId) {
      const user = mockUsers.find((u) => u.id === userId);
      if (user) {
        return HttpResponse.json({ user });
      }
    }

    return HttpResponse.json({ error: 'Invalid token' }, { status: 401 });
  }),

  http.post('/api/support', async ({ request }) => {
    const body = await request.json();
    return HttpResponse.json({
      message: 'Successful Support Request',
      body: body
    });
  }),

  http.post('/api/note', async ({ request }) => {
    await delay(1000); // Simulate network delay

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return new HttpResponse(JSON.stringify({ error: 'No file provided' }), {
        status: 400
      });
    }

    // Mock successful response returning UploadedFile type
    return HttpResponse.json({
      file: file,
      name: file.name || 'Aufnahme-KI-note.mp3',
      size: file.size || 75497472,
      type: file.type || 'audio/mpeg',
      url: URL.createObjectURL(file),
      duration: '07:00',
      id: 'file-' + Math.random().toString(36),
      transcription: {
        id: 'transcription-' + Math.random().toString(36),
        status: 'processing',
        estimatedCompletionTime: 5,
        message: 'File uploaded and transcription started successfully'
      }
    });
  }),

  http.get('/api/transcription/status', () => {
    return HttpResponse.json({
      isProcessing: false,
      fileId: null,
      progress: 0,
      estimatedTime: null
    });
  }),

  http.get('/api/transcription/progress', () => {
    const progress = Math.min(100, Math.floor(Math.random() * 125));
    const status = progress >= 100 ? 'completed' : 'processing';

    return HttpResponse.json({
      progress: progress,
      status: status,
      estimatedTime: progress < 100 ? 3 : null
    } as TranscriptionProgress);
  }),

  http.get('/api/download/pdf', () => {
    return HttpResponse.redirect('/144.pdf');
  }),

  http.get('/api/download/docx', () => {
    return HttpResponse.redirect('/144.docx');
  })
];
