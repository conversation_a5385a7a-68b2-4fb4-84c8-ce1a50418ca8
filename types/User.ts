/* eslint-disable no-unused-vars */

export interface UserResponse {
  user: User;
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  businessName: string;
  phoneNumber: string;
  email: string;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  minutes: number;
  features: string[];
  isPopular?: boolean;
}

export class User {
  constructor(
    public id: number,
    public name: string,
    public initials: string,
    public totalMinutes: number,
    public minutesLeft: number,
    public subscriptionType: string,
    public profile?: UserProfile,
    public email?: string
  ) {}
}
