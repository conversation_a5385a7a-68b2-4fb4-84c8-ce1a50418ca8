import { defineStore } from 'pinia';
import { Note } from '~/types/Note';
import type { NoteData, NoteStatus } from '~/types/Note';

interface NotesState {
  notes: Note[];
  isLoading: boolean;
  error: string | null;
}

export const useNotesStore = defineStore('notes', {
  state: (): NotesState => ({
    notes: [],
    isLoading: false,
    error: null
  }),

  getters: {
    // Get notes for current user
    userNotes: (state) => {
      const userStore = useUserStore();
      if (!userStore.user) return [];
      return state.notes.filter((note) => note.userId === userStore.user!.id);
    },

    // Get notes by status
    notesByStatus: (state) => (status: NoteStatus) => {
      const userStore = useUserStore();
      if (!userStore.user) return [];
      return state.notes.filter(
        (note) => note.userId === userStore.user!.id && note.status === status
      );
    },

    // Get notes by folder
    notesByFolder: (state) => (folder: string | null) => {
      const userStore = useUserStore();
      if (!userStore.user) return [];
      return state.notes.filter(
        (note) => note.userId === userStore.user!.id && note.folder === folder
      );
    },

    // Get note by ID
    getNoteById: (state) => (id: number) => {
      return state.notes.find((note) => note.id === id);
    },

    // Get processing notes count
    processingNotesCount: (state) => {
      const userStore = useUserStore();
      if (!userStore.user) return 0;
      return state.notes.filter(
        (note) =>
          note.userId === userStore.user!.id && note.status === 'processing'
      ).length;
    }
  },

  actions: {
    // Load user notes from API
    async loadUserNotes() {
      const userStore = useUserStore();

      // Wait for authentication to be established if needed
      if (!userStore.isAuthenticated || !userStore.user || !userStore.token) {
        this.error = 'User not authenticated';
        return;
      }

      this.isLoading = true;
      this.error = null;

      try {
        const response = await $fetch<{ notes: NoteData[] }>('/api/notes', {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${userStore.token}`
          }
        });

        this.notes = response.notes.map((noteData) => Note.fromJSON(noteData));
      } catch (error: unknown) {
        console.error('Failed to load notes:', error);

        // If it's an auth error, don't set a generic error message
        if (
          error &&
          typeof error === 'object' &&
          'status' in error &&
          error.status === 401
        ) {
          this.error = 'Authentication required. Please log in again.';
          // Clear auth if token is invalid
          userStore.clearAuth();
        } else {
          this.error = 'Failed to load notes';
        }
      } finally {
        this.isLoading = false;
      }
    },

    // Create a new note
    async createNote(noteData: {
      name: string;
      duration: string;
      folder: string | null;
      fileId?: string;
      transcriptionId?: string;
      fileUrl?: string;
      fileSize?: number;
      fileType?: string;
    }): Promise<Note | null> {
      const userStore = useUserStore();
      if (!userStore.user) {
        this.error = 'User not authenticated';
        return null;
      }

      this.isLoading = true;
      this.error = null;

      try {
        // Create note locally first
        const newNote = new Note(
          Date.now(), // Temporary ID, will be replaced by server
          noteData.name,
          new Date(),
          noteData.duration,
          noteData.folder,
          'processing',
          userStore.user.id,
          {
            fileId: noteData.fileId,
            transcriptionId: noteData.transcriptionId,
            fileUrl: noteData.fileUrl,
            fileSize: noteData.fileSize,
            fileType: noteData.fileType
          }
        );

        // Add to local store immediately for UI responsiveness
        this.notes.push(newNote);

        // Save to server (this would normally happen during file upload)
        // For now, we'll just return the note as the API call happens elsewhere
        return newNote;
      } catch (error) {
        console.error('Failed to create note:', error);
        this.error = 'Failed to create note';
        return null;
      } finally {
        this.isLoading = false;
      }
    },

    // Update note status
    updateNoteStatus(noteId: number, status: NoteStatus) {
      const note = this.notes.find((n) => n.id === noteId);
      if (note) {
        note.updateStatus(status);
      }
    },

    // Update note with server data
    updateNote(noteId: number, updates: Partial<NoteData>) {
      const noteIndex = this.notes.findIndex((n) => n.id === noteId);
      if (noteIndex !== -1) {
        const currentNote = this.notes[noteIndex];
        const updatedNoteData = { ...currentNote.toJSON(), ...updates };
        this.notes[noteIndex] = Note.fromJSON(updatedNoteData);
      }
    },

    // Delete a note
    async deleteNote(noteId: number): Promise<boolean> {
      const userStore = useUserStore();
      if (!userStore.user) {
        this.error = 'User not authenticated';
        return false;
      }

      this.isLoading = true;
      this.error = null;

      try {
        await $fetch(`/api/notes/${noteId}`, {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${userStore.token}`
          }
        });

        // Remove from local store
        this.notes = this.notes.filter((note) => note.id !== noteId);
        return true;
      } catch (error) {
        console.error('Failed to delete note:', error);
        this.error = 'Failed to delete note';
        return false;
      } finally {
        this.isLoading = false;
      }
    },

    // Download note
    async downloadNote(
      noteId: number,
      format: 'pdf' | 'docx'
    ): Promise<boolean> {
      const userStore = useUserStore();
      if (!userStore.user) {
        this.error = 'User not authenticated';
        return false;
      }

      try {
        const response = await fetch(
          `/api/notes/${noteId}/download/${format}`,
          {
            method: 'GET',
            headers: {
              Authorization: `Bearer ${userStore.token}`
            }
          }
        );

        if (!response.ok) throw new Error('Failed to download');

        const blob = await response.blob();
        const disposition = response.headers.get('Content-Disposition');
        const match = disposition?.match(/filename="?(.+?)"?$/);
        const filename = match?.[1] || `note-${noteId}.${format}`;

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        window.URL.revokeObjectURL(url);

        return true;
      } catch (error) {
        console.error('Failed to download note:', error);
        this.error = 'Failed to download note';
        return false;
      }
    },

    // Clear error
    clearError() {
      this.error = null;
    },

    // Clear all notes (for logout)
    clearNotes() {
      this.notes = [];
      this.error = null;
      this.isLoading = false;
    }
  }
});
