import { useUserStore } from '~/stores/user';

export default defineNuxtPlugin(() => {
  const userStore = useUserStore();

  // Only initialize auth on client side
  if (import.meta.client) {
    // Immediately try to restore session synchronously
    userStore.restoreSession();

    // Set up auth synchronization across tabs/windows
    const { setupSyncListeners } = useAuthSync();
    const cleanup = setupSyncListeners();

    // Store cleanup function for potential future use
    if (cleanup) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any).__authSyncCleanup = cleanup;
    }
  }
});
