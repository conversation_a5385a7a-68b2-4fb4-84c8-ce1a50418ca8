import { useUserStore } from '~/stores/user';

export default defineNuxtPlugin(async () => {
  const userStore = useUserStore();

  // Only initialize auth on client side
  if (import.meta.client) {
    // Initialize authentication properly with token verification
    await userStore.initializeAuth();

    // Set up auth synchronization across tabs/windows
    const { setupSyncListeners } = useAuthSync();
    const cleanup = setupSyncListeners();

    // Store cleanup function for potential future use
    if (cleanup) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any).__authSyncCleanup = cleanup;
    }
  }
});
