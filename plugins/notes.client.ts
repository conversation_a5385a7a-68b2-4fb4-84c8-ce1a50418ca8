import { useNotesStore } from '~/stores/notes';
import { useUserStore } from '~/stores/user';

export default defineNuxtPlugin(async () => {
  const notesStore = useNotesStore();
  const userStore = useUserStore();

  // Watch for user authentication changes
  watch(() => userStore.isAuthenticated, async (isAuthenticated) => {
    if (isAuthenticated && userStore.user) {
      // Load user notes when authenticated
      await notesStore.loadUserNotes();
    } else {
      // Clear notes when logged out
      notesStore.clearNotes();
    }
  }, { immediate: true });
});
