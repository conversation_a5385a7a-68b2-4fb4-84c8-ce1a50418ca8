import { useNotesStore } from '~/stores/notes';
import { useUserStore } from '~/stores/user';

export default defineNuxtPlugin(async () => {
  const notesStore = useNotesStore();
  const userStore = useUserStore();

  // Watch for user authentication changes
  watch(
    () => userStore.isAuthenticated,
    async (isAuthenticated, wasAuthenticated) => {
      if (isAuthenticated && userStore.user && userStore.token) {
        // Load user notes when authenticated
        await notesStore.loadUserNotes();
      } else if (wasAuthenticated && !isAuthenticated) {
        // Clear notes when logged out
        notesStore.clearNotes();
      }
    },
    { immediate: true }
  );
});
